package main

import (
	"encoding/json"
	"fmt"
	"newsBot/internal/result"
)

func main() {
	// 测试成功结果
	successResult := result.SuccessResult("测试数据")
	successJSON, _ := json.MarshalIndent(successResult, "", "  ")
	fmt.Println("成功结果:")
	fmt.Println(string(successJSON))
	fmt.Println()

	// 测试错误结果
	errorResult := result.ErrorReqParam
	errorJSON, _ := json.MarshalIndent(errorResult, "", "  ")
	fmt.Println("错误结果:")
	fmt.Println(string(errorJSON))
	fmt.Println()

	// 测试带错误信息的结果
	err := fmt.Errorf("测试错误")
	errorWithMsgResult := result.ErrorSelect.AddError(err)
	errorWithMsgJSON, _ := json.MarshalIndent(errorWithMsgResult, "", "  ")
	fmt.Println("带错误信息的结果:")
	fmt.Println(string(errorWithMsgJSON))
	fmt.Println()

	// 测试列表结果
	listData := []string{"item1", "item2", "item3"}
	listResult := result.ListResult(listData, 1, 10, 3)
	listJSON, _ := json.MarshalIndent(listResult, "", "  ")
	fmt.Println("列表结果:")
	fmt.Println(string(listJSON))
}
