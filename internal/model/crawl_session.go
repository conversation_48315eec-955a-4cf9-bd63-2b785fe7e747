package model

import (
	"time"

	"gorm.io/gorm"
)

// CrawlSession 爬取会话记录
type CrawlSession struct {
	Id          int64      `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`
	StartTime   time.Time  `gorm:"column:start_time;not null;comment:开始时间" json:"start_time"`
	EndTime     *time.Time `gorm:"column:end_time;comment:结束时间" json:"end_time,omitempty"`
	TotalNews   int        `gorm:"column:total_news;default:0;comment:总新闻数" json:"total_news"`
	SuccessNews int        `gorm:"column:success_news;default:0;comment:成功新闻数" json:"success_news"`
	Status      string     `gorm:"column:status;not null;default:'running';comment:状态" json:"status"` // running, completed, failed
	ErrorMsg    string     `gorm:"column:error_msg;comment:错误信息" json:"error_msg,omitempty"`
	CreatedAt   time.Time  `gorm:"column:created_at;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`
}

// TableName 指定表名
func (CrawlSession) TableName() string {
	return "crawl_sessions"
}

// CrawlSessionModel 爬取会话模型
type CrawlSessionModel struct {
	db *gorm.DB
}

// NewCrawlSessionModel 创建爬取会话模型
func NewCrawlSessionModel(db *gorm.DB) *CrawlSessionModel {
	return &CrawlSessionModel{
		db: db,
	}
}

// Create 创建爬取会话
func (m *CrawlSessionModel) Create(session *CrawlSession) error {
	return m.db.Create(session).Error
}

// UpdateById 根据ID更新记录
func (m *CrawlSessionModel) UpdateById(id int64, updates map[string]interface{}) error {
	return m.db.Model(&CrawlSession{}).Where("id = ?", id).Updates(updates).Error
}

// FindWithLimit 获取记录列表（带限制）
func (m *CrawlSessionModel) FindWithLimit(limit int) ([]*CrawlSession, error) {
	var sessions []*CrawlSession

	db := m.db.Order("start_time DESC")
	if limit > 0 {
		db = db.Limit(limit)
	}

	if err := db.Find(&sessions).Error; err != nil {
		return nil, err
	}

	return sessions, nil
}

// FindById 根据ID获取记录
func (m *CrawlSessionModel) FindById(id int64) (*CrawlSession, error) {
	var session CrawlSession
	if err := m.db.First(&session, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &session, nil
}

// List 获取爬取会话列表（带分页和条件）
func (m *CrawlSessionModel) List(page, pageSize int, conditions map[string]interface{}) ([]*CrawlSession, int64, error) {
	var sessions []*CrawlSession
	var total int64

	db := m.db.Model(&CrawlSession{})

	// 添加查询条件
	for key, value := range conditions {
		if strValue, ok := value.(string); ok && strValue != "" {
			if key == "status" {
				db = db.Where(key+" = ?", value)
			} else {
				db = db.Where(key+" LIKE ?", "%"+strValue+"%")
			}
		} else if value != nil {
			db = db.Where(key+" = ?", value)
		}
	}

	// 分页查询
	if page > 0 && pageSize > 0 {
		// 获取总数
		if err := db.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		db = db.Offset((page - 1) * pageSize).Limit(pageSize)
	}

	db = db.Order("start_time DESC")
	if err := db.Find(&sessions).Error; err != nil {
		return nil, 0, err
	}

	return sessions, total, nil
}
