package service

import (
	"time"

	"newsBot/internal/model"
)

// SessionService 会话业务服务
type SessionService struct {
	crawlSessionModel   *model.CrawlSessionModel
	hotlistSessionModel *model.HotlistSessionModel
}

// NewSessionService 创建会话服务
func NewSessionService(crawlSessionModel *model.CrawlSessionModel, hotlistSessionModel *model.HotlistSessionModel) *SessionService {
	return &SessionService{
		crawlSessionModel:   crawlSessionModel,
		hotlistSessionModel: hotlistSessionModel,
	}
}

// StartCrawlSession 开始新的爬取会话
func (s *SessionService) StartCrawlSession() (int64, error) {
	session := &model.CrawlSession{
		StartTime: time.Now(),
		Status:    "running",
	}

	if err := s.crawlSessionModel.Create(session); err != nil {
		return 0, err
	}

	return session.Id, nil
}

// EndCrawlSession 结束爬取会话
func (s *SessionService) EndCrawlSession(sessionId int64, totalNews, successNews int, status string, errorMsg string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"end_time":     &now,
		"total_news":   totalNews,
		"success_news": successNews,
		"status":       status,
		"error_msg":    errorMsg,
	}

	return s.crawlSessionModel.UpdateById(sessionId, updates)
}

// StartHotlistSession 开始新的热搜会话
func (s *SessionService) StartHotlistSession() (int64, error) {
	session := &model.HotlistSession{
		StartTime: time.Now(),
		Status:    "running",
	}

	if err := s.hotlistSessionModel.Create(session); err != nil {
		return 0, err
	}

	return session.Id, nil
}

// EndHotlistSession 结束热搜会话
func (s *SessionService) EndHotlistSession(sessionId int64, totalItems, successItems int, status string, errorMsg string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"end_time":      &now,
		"total_items":   totalItems,
		"success_items": successItems,
		"status":        status,
		"error_msg":     errorMsg,
	}

	return s.hotlistSessionModel.UpdateById(sessionId, updates)
}
