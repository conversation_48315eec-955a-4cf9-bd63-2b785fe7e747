package service

import (
	"time"

	"newsBot/internal/model"
	"newsBot/internal/types"
)

// NewsService 新闻业务服务
type NewsService struct {
	newsRecordModel *model.NewsRecordModel
}

// NewNewsService 创建新闻服务
func NewNewsService(newsRecordModel *model.NewsRecordModel) *NewsService {
	return &NewsService{
		newsRecordModel: newsRecordModel,
	}
}

// SaveNewsRecord 保存新闻记录（包含业务逻辑）
func (s *NewsService) SaveNewsRecord(sessionId int64, news types.NewsItem) error {
	// 计算质量评分
	qualityScore := s.calculateQualityScore(news)

	hasContent := len(news.Content) > 20
	contentLength := len(news.Content)

	record := &model.NewsRecord{
		SessionId:     sessionId,
		Source:        news.Source,
		Title:         news.Title,
		URL:           news.URL,
		Content:       news.Content,
		Summary:       news.Summary,
		Author:        news.Author,
		PublishTime:   news.PublishTime,
		WordCount:     news.WordCount,
		QualityScore:  qualityScore,
		HasContent:    hasContent,
		ContentLength: contentLength,
		CreatedAt:     time.Now(),
	}

	// 使用model层的Save方法
	return s.newsRecordModel.Save(record)
}

// calculateQualityScore 计算新闻质量评分
func (s *NewsService) calculateQualityScore(news types.NewsItem) float64 {
	score := 0.0

	// 标题质量 (20分)
	if len(news.Title) > 10 && len(news.Title) < 100 {
		score += 20
	} else if len(news.Title) > 5 {
		score += 10
	}

	// 内容质量 (40分)
	contentLen := len(news.Content)
	if contentLen > 1000 {
		score += 40
	} else if contentLen > 500 {
		score += 30
	} else if contentLen > 100 {
		score += 20
	} else if contentLen > 20 {
		score += 10
	}

	// 摘要质量 (15分)
	if len(news.Summary) > 50 && len(news.Summary) < 500 {
		score += 15
	} else if len(news.Summary) > 20 {
		score += 10
	}

	// 作者信息 (10分)
	if news.Author != "" {
		score += 10
	}

	// 发布时间 (10分)
	if news.PublishTime != "" {
		score += 10
	}

	// URL有效性 (5分)
	if news.URL != "" && (len(news.URL) > 20) {
		score += 5
	}

	return score
}
