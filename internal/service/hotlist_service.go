package service

import (
	"time"

	"newsBot/internal/model"
	"newsBot/internal/types"
)

// HotlistService 热搜业务服务
type HotlistService struct {
	hotlistRecordModel *model.HotlistRecordModel
}

// NewHotlistService 创建热搜服务
func NewHotlistService(hotlistRecordModel *model.HotlistRecordModel) *HotlistService {
	return &HotlistService{
		hotlistRecordModel: hotlistRecordModel,
	}
}

// SaveHotlistRecord 保存热搜记录（包含业务逻辑）
func (s *HotlistService) SaveHotlistRecord(sessionId int64, item types.HotItem) error {
	record := &model.HotlistRecord{
		SessionId:   sessionId,
		Platform:    item.Platform,
		Title:       item.Title,
		Description: item.Description,
		URL:         item.URL,
		HotValue:    item.HotValue,
		Rank:        item.Rank,
		CreatedAt:   time.Now(),
	}

	return s.hotlistRecordModel.Create(record)
}
