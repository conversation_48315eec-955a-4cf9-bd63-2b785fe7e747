package handler

import (
	"log"
	"newsBot/internal/result"
	"newsBot/internal/svc"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QualityHandler 质量监控处理器
type QualityHandler struct {
	svcCtx *svc.ServiceContext
}

// NewQualityHandler 创建质量监控处理器
func NewQualityHandler(svcCtx *svc.ServiceContext) *QualityHandler {
	return &QualityHandler{
		svcCtx: svcCtx,
	}
}

// GetSessions 获取爬取会话列表
func (h *QualityHandler) GetSessions(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 100 {
		limit = 20
	}

	// 查询会话列表
	sessions, err := h.svcCtx.CrawlSessionModel.FindWithLimit(limit)
	if err != nil {
		log.Printf("查询爬取会话失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(sessions))
}

// GetNews 获取指定会话的新闻列表
func (h *QualityHandler) GetNews(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取查询参数
	sessionIDStr := c.Query("session_id")
	if sessionIDStr == "" {
		c.JSON(200, result.ErrorReqParam)
		return
	}

	sessionID, err := strconv.ParseInt(sessionIDStr, 10, 64)
	if err != nil {
		c.JSON(200, result.ErrorReqParam.AddError(err))
		return
	}

	limitStr := c.DefaultQuery("limit", "50")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 || limit > 200 {
		limit = 50
	}

	// 查询新闻列表
	news, err := h.svcCtx.NewsRecordModel.FindBySessionId(sessionID, limit)
	if err != nil {
		log.Printf("查询新闻记录失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(news))
}

// GetStats 获取质量统计信息
func (h *QualityHandler) GetStats(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取统计信息
	stats, err := h.svcCtx.NewsRecordModel.GetQualityStats()
	if err != nil {
		log.Printf("获取质量统计失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(stats))
}

// GetDetail 获取新闻详情
func (h *QualityHandler) GetDetail(c *gin.Context) {
	if h.svcCtx == nil {
		c.JSON(200, result.ErrorServiceNotAvail)
		return
	}

	// 获取查询参数
	idStr := c.Query("id")
	if idStr == "" {
		c.JSON(200, result.ErrorReqParam)
		return
	}

	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(200, result.ErrorReqParam.AddError(err))
		return
	}

	// 查询新闻详情
	news, err := h.svcCtx.NewsRecordModel.GetNewsRecordByID(id)
	if err != nil {
		log.Printf("查询新闻详情失败: %v", err)
		c.JSON(200, result.ErrorSelect.AddError(err))
		return
	}

	c.JSON(200, result.SuccessResult(news))
}
