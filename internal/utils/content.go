package utils

import (
	"log"
	"regexp"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"github.com/go-resty/resty/v2"
)

// ExtractContentFromURL 从URL提取新闻内容摘要（已废弃，使用ExtractFullContentFromURL）
// Deprecated: 使用 ExtractFullContentFromURL 替代
func ExtractContentFromURL(client *resty.Client, url string) string {
	return ExtractFullContentFromURL(client, url)
}

// ExtractFullContentFromURL 从URL提取完整新闻内容
func ExtractFullContentFromURL(client *resty.Client, url string) string {
	resp, err := client.R().Get(url)
	if err != nil {
		log.Printf("获取页面失败 %s: %v", url, err)
		return ""
	}

	content := resp.String()
	return ExtractFullContentFromHTML(content, url)
}

// ExtractContentFromHTML 从HTML内容中提取摘要（已废弃，使用ExtractFullContentFromHTML）
// Deprecated: 使用 ExtractFullContentFromHTML 替代
func ExtractContentFromHTML(htmlContent string) string {
	// 为了向后兼容，调用新的完整内容提取方法并截取前200字符
	fullContent := ExtractFullContentFromHTML(htmlContent, "")
	if len(fullContent) > 200 {
		return fullContent[:200] + "..."
	}
	return fullContent
}

// ExtractFullContentFromHTML 从HTML内容中提取完整新闻正文
func ExtractFullContentFromHTML(htmlContent, url string) string {
	// 根据不同网站使用不同的提取策略
	if strings.Contains(url, "xinhuanet.com") {
		return extractXinhuaContent(htmlContent)
	} else if strings.Contains(url, "people.com.cn") {
		return extractPeopleContent(htmlContent)
	} else if strings.Contains(url, "cctv.com") {
		return extractCCTVContent(htmlContent)
	} else if strings.Contains(url, "thepaper.cn") {
		return extractThePaperContent(htmlContent)
	}

	// 通用提取方法
	return extractGenericContent(htmlContent)
}

// CleanText 清理文本内容
func CleanText(text string) string {
	// 移除多余的空白字符
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")

	// 移除HTML标签
	text = regexp.MustCompile(`<[^>]*>`).ReplaceAllString(text, "")

	// 移除特殊字符
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")

	// 移除换行符和制表符
	text = strings.ReplaceAll(text, "\n", " ")
	text = strings.ReplaceAll(text, "\t", " ")
	text = strings.ReplaceAll(text, "\r", " ")

	return strings.TrimSpace(text)
}

// extractXinhuaContent 提取新华网新闻内容
func extractXinhuaContent(htmlContent string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	// 新华网内容选择器
	selectors := []string{
		"#detail",
		".article",
		".content",
		"#content",
		".main-content",
		".news-content",
		"div[id*='content']",
		"div[class*='content']",
	}

	for _, selector := range selectors {
		content := doc.Find(selector).First()
		if content.Length() > 0 {
			// 移除不需要的元素
			content.Find("script, style, .ad, .advertisement, .share, .comment").Remove()

			text := content.Text()
			text = CleanFullText(text)

			if len(text) > 200 { // 确保是完整内容
				return text
			}
		}
	}

	return extractGenericContent(htmlContent)
}

// extractPeopleContent 提取人民网新闻内容
func extractPeopleContent(htmlContent string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	// 人民网内容选择器
	selectors := []string{
		".rm_txt_con",
		".box_con",
		".article_box",
		".text_con",
		"#rwb_zw",
		".content",
		"div[class*='text']",
	}

	for _, selector := range selectors {
		content := doc.Find(selector).First()
		if content.Length() > 0 {
			content.Find("script, style, .ad, .share").Remove()

			text := content.Text()
			text = CleanFullText(text)

			if len(text) > 200 {
				return text
			}
		}
	}

	return extractGenericContent(htmlContent)
}

// extractCCTVContent 提取央视网新闻内容
func extractCCTVContent(htmlContent string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	// 央视网内容选择器
	selectors := []string{
		".content_area",
		".cnt_bd",
		".article_text",
		".text_content",
		"#content_area",
		".main_text",
		"div[class*='content']",
	}

	for _, selector := range selectors {
		content := doc.Find(selector).First()
		if content.Length() > 0 {
			content.Find("script, style, .ad, .video").Remove()

			text := content.Text()
			text = CleanFullText(text)

			if len(text) > 200 {
				return text
			}
		}
	}

	return extractGenericContent(htmlContent)
}

// extractThePaperContent 提取澎湃新闻内容
func extractThePaperContent(htmlContent string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	// 澎湃新闻内容选择器
	selectors := []string{
		".news_txt",
		".content",
		".article_content",
		".news_content",
		"div[class*='news_txt']",
		"div[class*='content']",
	}

	for _, selector := range selectors {
		content := doc.Find(selector).First()
		if content.Length() > 0 {
			content.Find("script, style, .ad, .share, .related").Remove()

			text := content.Text()
			text = CleanFullText(text)

			if len(text) > 200 {
				return text
			}
		}
	}

	return extractGenericContent(htmlContent)
}

// extractGenericContent 通用内容提取方法
func extractGenericContent(htmlContent string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		return ""
	}

	// 通用选择器，按优先级排序
	selectors := []string{
		"article",
		".article",
		".content",
		".post-content",
		".entry-content",
		".main-content",
		"#content",
		"#main",
		".text",
		"main",
	}

	for _, selector := range selectors {
		content := doc.Find(selector).First()
		if content.Length() > 0 {
			// 移除不需要的元素
			content.Find("script, style, nav, header, footer, .ad, .advertisement, .sidebar, .menu, .navigation, .share, .comment, .related").Remove()

			text := content.Text()
			text = CleanFullText(text)

			if len(text) > 200 {
				return text
			}
		}
	}

	// 如果以上都失败，尝试提取所有段落
	var paragraphs []string
	doc.Find("p").Each(func(i int, s *goquery.Selection) {
		text := strings.TrimSpace(s.Text())
		if len(text) > 20 {
			paragraphs = append(paragraphs, text)
		}
	})

	if len(paragraphs) > 0 {
		fullText := strings.Join(paragraphs, "\n\n")
		return CleanFullText(fullText)
	}

	return ""
}

// CleanFullText 清理完整文本内容（保留段落结构）
func CleanFullText(text string) string {
	// 移除HTML标签
	text = regexp.MustCompile(`<[^>]*>`).ReplaceAllString(text, "")

	// 处理HTML实体
	text = strings.ReplaceAll(text, "&nbsp;", " ")
	text = strings.ReplaceAll(text, "&amp;", "&")
	text = strings.ReplaceAll(text, "&lt;", "<")
	text = strings.ReplaceAll(text, "&gt;", ">")
	text = strings.ReplaceAll(text, "&quot;", "\"")
	text = strings.ReplaceAll(text, "&#39;", "'")

	// 移除多余的空白，但保留段落分隔
	text = regexp.MustCompile(`[ \t]+`).ReplaceAllString(text, " ")
	text = regexp.MustCompile(`\n\s*\n`).ReplaceAllString(text, "\n\n")

	// 移除开头和结尾的空白
	text = strings.TrimSpace(text)

	// 移除常见的无用文本
	unwantedPatterns := []string{
		`责任编辑：.*`,
		`来源：.*`,
		`编辑：.*`,
		`记者：.*\s*报道`,
		`本文来源：.*`,
		`\(.*记者.*\)`,
		`【.*】`,
		`相关阅读.*`,
		`延伸阅读.*`,
	}

	for _, pattern := range unwantedPatterns {
		re := regexp.MustCompile(pattern)
		text = re.ReplaceAllString(text, "")
	}

	return strings.TrimSpace(text)
}

// CreateClient 创建配置好的HTTP客户端
func CreateClient() *resty.Client {
	client := resty.New()

	// 设置超时
	client.SetTimeout(30 * time.Second)

	// 设置重试
	client.SetRetryCount(3)
	client.SetRetryWaitTime(5 * time.Second)

	// 设置User-Agent
	client.SetHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

	// 设置其他常见请求头
	client.SetHeader("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	client.SetHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	client.SetHeader("Accept-Encoding", "gzip, deflate, br")
	client.SetHeader("Connection", "keep-alive")
	client.SetHeader("Upgrade-Insecure-Requests", "1")

	return client
}

// ExtractSummaryFromTitle 从标题推断可能的摘要关键词
func ExtractSummaryFromTitle(title string) string {
	// 如果没有其他内容，至少从标题中提取一些信息
	if len(title) > 50 {
		return title[:50] + "..."
	}
	return title
}
