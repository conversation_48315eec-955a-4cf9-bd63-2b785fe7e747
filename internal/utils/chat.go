package utils

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"newsBot/internal/constant"
	"newsBot/internal/types"
)

func SendMessage(apiPlatform string, messageList []types.HistoryMessage) (*http.Response, error) {
	apiConfigMap := constant.ApiConfigMap()
	apiUrl := apiConfigMap[apiPlatform].Url
	secretKey := apiConfigMap[apiPlatform].SecretKey
	data := map[string]any{}
	switch apiPlatform {
	case constant.ApiPlatform_Firework:
		data = map[string]any{
			"model":             "accounts/fireworks/models/deepseek-r1",
			"max_tokens":        20480,
			"top_p":             1,
			"top_k":             40,
			"presence_penalty":  0,
			"frequency_penalty": 0,
			"temperature":       0.6,
			"stream":            true,
			"messages":          messageList,
		}
	case constant.ApiPlatform_OpenRouter:
		data = map[string]any{
			"model":    "anthropic/claude-3.5-sonnet",
			"messages": messageList,
		}
	default:
		return nil, errors.New("不支持的api平台")
	}

	return sendRequest(apiUrl, data, secretKey)
}

func SendMessageMulti(apiPlatform string, model string, messageList []types.HistoryMessage) (*http.Response, error) {
	apiConfigMap := constant.ApiConfigMap()
	apiUrl := apiConfigMap[apiPlatform].Url
	secretKey := apiConfigMap[apiPlatform].SecretKey
	//获取模型信息
	modelList := constant.GetModelMap()
	models := modelList[apiPlatform]
	data := map[string]any{}
	switch apiPlatform {
	case constant.ApiPlatform_Firework:
		data = map[string]any{
			"model":             "accounts/fireworks/models/" + models[model],
			"max_tokens":        20480,
			"top_p":             1,
			"top_k":             40,
			"presence_penalty":  0,
			"frequency_penalty": 0,
			"temperature":       0.6,
			"stream":            true,
			"messages":          messageList,
		}
	case constant.ApiPlatform_OpenRouter:
		data = map[string]any{
			"model":    models[model],
			"messages": messageList,
			"stream":   true,
		}
	case constant.ApiPlatform_Ali_baiLian:
		data = map[string]any{
			"model":    models[model],
			"messages": messageList,
			"stream":   true,
			"stream_options": map[string]any{
				"include_usage": true,
			},
		}

	default:
		return nil, errors.New("不支持的api平台")
	}
	log.Printf("对话平台: %s ,url:", apiPlatform, apiUrl)
	return sendRequest(apiUrl, data, secretKey)
}

// sendRequest 发送请求
func sendRequest(apiUrl string, data any, secretKey string) (*http.Response, error) {
	//log.Printf("发送请求到: %s", apiUrl)
	//log.Printf("SecretKey: %s", secretKey)
	jsonData, err := json.Marshal(data)
	//log.Printf("请求数据: %v", string(jsonData))
	if err != nil {
		return nil, err
	}
	req, err := http.NewRequest(http.MethodPost, apiUrl, bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+secretKey)

	//代理配置
	transport := &http.Transport{
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   20,                // 增加每个主机的最大空闲连接数
		MaxConnsPerHost:       20,                // 增加每个主机的最大连接数
		IdleConnTimeout:       120 * time.Second, // 增加空闲连接超时时间
		TLSHandshakeTimeout:   30 * time.Second,  // 增加TLS握手超时时间
		ExpectContinueTimeout: 20 * time.Second,  // 增加期望继续超时时间
		DisableCompression:    true,
		DisableKeepAlives:     false,
		ForceAttemptHTTP2:     true,
		DialContext: (&net.Dialer{
			Timeout:   60 * time.Second, // 增加拨号超时时间
			KeepAlive: 60 * time.Second, // 增加保持活动时间
			DualStack: true,
		}).DialContext,
		// 增加响应体的读取超时
		ResponseHeaderTimeout: 60 * time.Second, // 增加响应头超时时间
	}

	// 不使用代理，直接连接

	client := &http.Client{
		Transport: transport,
		Timeout:   180 * time.Second,
	}

	// 添加重试机制
	maxRetries := 5
	retryInterval := 5 * time.Second
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			time.Sleep(retryInterval)
			log.Printf("每次重试间隔%d秒", int(retryInterval.Seconds()))
		}

		resp, err := client.Do(req)
		if err == nil {
			//成功返回读取数据
			if resp.StatusCode == http.StatusOK {
				return resp, nil
			}
			resp.Body.Close()
			log.Printf("HTTP状态码错误: %d", resp.StatusCode)
			lastErr = fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
			continue
		}

		// 检查是否是临时性错误
		if netErr, ok := err.(net.Error); ok && (netErr.Temporary() || netErr.Timeout()) {
			lastErr = err
			log.Printf("遇到临时性网络错误: %v, 正在进行第%d次重试", err, i+1)

			// 增加更详细的网络错误信息输出
			if netErr.Timeout() {
				log.Printf("错误详情: 连接超时 - 请检查服务器响应时间或网络状况")
			} else if netErr.Temporary() {
				log.Printf("错误详情: 临时性网络问题 - 可能是网络拥塞或服务器负载过高")
			}
			continue
		}

		// 对不同类型的网络错误进行更详细的分类和输出
		if urlErr, ok := err.(*url.Error); ok {
			lastErr = err
			log.Printf("请求失败，正在进行第%d次重试: %v", i+1, err)

			// 详细分析URL错误
			if opErr, ok := urlErr.Err.(*net.OpError); ok {
				log.Printf("错误详情: 网络操作错误 - 操作类型: %s, 网络类型: %s", opErr.Op, opErr.Net)

				// 进一步分析底层错误
				if syscallErr, ok := opErr.Err.(*os.SyscallError); ok {
					log.Printf("系统调用错误: %s - %v", syscallErr.Syscall, syscallErr.Err)
				} else {
					log.Printf("底层错误: %v", opErr.Err)
				}
			} else {
				log.Printf("URL错误详情: %v", urlErr.Err)
			}
			continue
		}

		// 如果是其他类型的错误，也继续重试
		lastErr = err
		log.Printf("请求失败，正在进行第%d次重试: %v", i+1, err)
	}

	// 增强最终错误信息的输出
	if lastErr != nil {
		errMsg := fmt.Sprintf("在%d次尝试后仍然失败: %v", maxRetries, lastErr)

		// 添加更多错误上下文信息
		if netErr, ok := lastErr.(net.Error); ok {
			if netErr.Timeout() {
				errMsg += "\n连接超时问题: 可能是目标服务器无响应或网络延迟过高"
			} else if netErr.Temporary() {
				errMsg += "\n临时网络问题: 建议稍后重试"
			}
		}

		// 添加针对RPC连接问题的特定提示
		if strings.Contains(lastErr.Error(), "layer=rpc") || strings.Contains(lastErr.Error(), "tcp") {
			errMsg += "\nRPC连接问题: 检查本地服务是否正常运行，端口是否被占用或防火墙设置"
		}
		log.Printf(errMsg)
		return nil, fmt.Errorf("%s", errMsg)
	}

	return nil, fmt.Errorf("在%d次尝试后仍然失败: 未知错误", maxRetries)
}

func WriteResponse(c *gin.Context, resp *http.Response) (string, string) {
	defer resp.Body.Close() // 确保响应体被关闭
	w := c.Writer
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	// 刷新缓冲区
	w.Flush()
	// 使用 bufio 逐行读取 SSE 数据
	reply := ""
	think := ""
	reader := bufio.NewReader(resp.Body)
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			// 如果遇到 EOF 表示服务端结束了连接
			if err == io.EOF {
				fmt.Println("收到 EOF，连接结束")
				break
			}
			// 处理超时和网络错误
			log.Printf("读取 SSE 数据出错: %v", err)

			// 检查是否是超时错误
			if strings.Contains(err.Error(), "deadline exceeded") ||
				strings.Contains(err.Error(), "timeout") ||
				strings.Contains(err.Error(), "connection") {
				log.Printf("检测到网络错误或超时，停止读取")
				break
			}

			// 其他错误也退出循环，避免死循环
			break
		}

		// 检查客户端连接是否已关闭
		if c.Request.Context().Err() != nil {
			log.Printf("客户端连接已关闭: %v", c.Request.Context().Err())
			break
		}

		_, err = fmt.Fprintf(w, line)
		if err != nil {
			fmt.Println("发送 SSE 数据出错: ", err)
			break
		}
		w.Flush() // 刷新缓冲区，发送数据到客户端
		// 去除首尾空白
		line = strings.TrimSpace(line)
		if line == "" {
			// 有时候换行代表事件的分隔，这里可以根据需要处理
			continue
		}

		// 解析 SSE：通常以 "data:" 开头的行才是你关心的主体数据
		if strings.HasPrefix(line, "data:") {
			data := strings.TrimPrefix(line, "data:")
			data = strings.TrimSpace(data)

			// 一些 SSE 流会用 [DONE] 代表流结束
			if data == "[DONE]" {
				fmt.Println("数据流发送完毕，收到 [DONE] 标记，结束读取")
				break
			}
			// 这里把 data 内容打印出来，或者进行 JSON 解析
			// fmt.Println("SSE data:", data)
			// respInfo := types.StreamResponse{}
			// json.Unmarshal([]byte(data), &respInfo)
			// reply += respInfo.Choices[0].Delta.Content
			respInfo := types.StreamResponse{}
			if err := json.Unmarshal([]byte(data), &respInfo); err != nil {
				log.Printf("解析 JSON 数据失败: %v, 原始数据: %s", err, data)
				continue
			}

			// 检查数据结构是否符合预期
			if len(respInfo.Choices) > 0 {
				reply += respInfo.Choices[0].Delta.Content
			} else {
				log.Printf("收到的数据结构不符合预期: %s", data)
			}
		}
	}

	thinkIndex := strings.Index(reply, "</think>")
	if thinkIndex != -1 {
		think = reply[:thinkIndex+len("</think>")]
		reply = reply[thinkIndex+len("</think>"):]
	} else {
		think = reply + "</think>"
		reply = ""
	}

	return reply, think
}

// 不同平台响应格式不太一样
// 本来计划诗后端处理统一sse json格式在推送给前端,但是感觉太消耗后端性能
// 再看情况改吧

func WriteResponseMulti(apiPlatform string, model string, c *gin.Context, resp *http.Response) (string, string) {
	defer resp.Body.Close() // 确保响应体被关闭

	w := c.Writer
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	// 刷新缓冲区
	w.Flush()
	// 使用 bufio 逐行读取 SSE 数据
	reply := ""
	think := ""
	reader := bufio.NewReader(resp.Body)
	//记录下读取时间
	startTime := time.Now()
	for {
		line, err := reader.ReadString('\n')
		if err != nil {
			// 如果遇到 EOF 表示服务端结束了连接
			if err == io.EOF {
				fmt.Println("收到 EOF，连接结束")
				break
			}
			// 处理超时和网络错误
			log.Printf("读取 SSE 数据出错: %v", err)

			endTime := time.Now()
			log.Printf("从 %v 到 %v 花费了 %v\n", startTime, endTime, endTime.Sub(startTime))

			// 检查是否是超时错误
			if strings.Contains(err.Error(), "deadline exceeded") ||
				strings.Contains(err.Error(), "timeout") ||
				strings.Contains(err.Error(), "connection") {
				log.Printf("检测到网络错误或超时，停止读取")
				break
			}

			// 其他错误也退出循环，避免死循环
			break
		}

		// 检查客户端连接是否已关闭
		if c.Request.Context().Err() != nil {
			log.Printf("客户端连接已关闭: %v", c.Request.Context().Err())
			break
		}

		// 直接使用 Write 方法而不是 Fprintf，减少格式化开销
		if _, err = w.Write([]byte(line)); err != nil {
			log.Printf("发送 SSE 数据出错: %v", err)
			break
		}
		// 立即刷新缓冲区，确保数据发送到客户端
		w.Flush()
		// 去除首尾空白
		line = strings.TrimSpace(line)
		if line == "" {
			// 有时候换行代表事件的分隔，这里可以根据需要处理
			continue
		}

		// 解析 SSE：通常以 "data:" 开头的行才是你关心的主体数据
		if strings.HasPrefix(line, "data:") {
			data := strings.TrimPrefix(line, "data:")
			data = strings.TrimSpace(data)

			// 一些 SSE 流会用 [DONE] 代表流结束
			if data == "[DONE]" {
				fmt.Println("数据流发送完毕，收到 [DONE] 标记，结束读取")
				break
			}

			switch apiPlatform {
			case constant.ApiPlatform_Firework:
				{
					respInfo := types.StreamResponse{}
					if err := json.Unmarshal([]byte(data), &respInfo); err != nil {
						log.Printf("解析 JSON 数据失败: %v, 原始数据: %s", err, data)
						continue
					}

					// 检查数据结构是否符合预期
					if len(respInfo.Choices) > 0 {
						reply += respInfo.Choices[0].Delta.Content
					} else {
						log.Printf("收到的数据结构不符合预期: %s", data)
					}
				}
			case constant.ApiPlatform_Ali_baiLian:
				{
					switch model {
					case constant.ALiBaiLian_deepseek_r1:
						respInfo := types.AliDeepSeekResponse{}
						if err := json.Unmarshal([]byte(data), &respInfo); err != nil {
							log.Printf("解析 JSON 数据失败: %v, 原始数据: %s", err, data)
							continue
						}
						// 检查数据结构是否符合预期
						if len(respInfo.Choices) > 0 {
							reply += respInfo.Choices[0].Delta.Content
							think += respInfo.Choices[0].Delta.ReasoningContent
						} else {
							log.Printf("收到的数据结构不符合预期: %s", data)
						}
					}
				}
			case constant.ApiPlatform_OpenRouter:
				{
					//openRouter目前看来基本都一致,就是多了个 reasoning
					respInfo := types.OpenRouterResponse{}
					if err := json.Unmarshal([]byte(data), &respInfo); err != nil {
						log.Printf("解析 JSON 数据失败: %v, 原始数据: %s", err, data)
						continue
					}
					// 检查数据结构是否符合预期
					if len(respInfo.Choices) > 0 {
						reply += respInfo.Choices[0].Delta.Content
						think += respInfo.Choices[0].Delta.Reasoning
					} else {
						log.Printf("收到的数据结构不符合预期: %s", data)
					}
				}

			}

		}
	}

	// Firework 平台的特殊处理
	switch apiPlatform {
	case constant.ApiPlatform_Firework:
		thinkIndex := strings.Index(reply, "</think>")
		if thinkIndex != -1 {
			think = reply[:thinkIndex+len("</think>")]
			reply = reply[thinkIndex+len("</think>"):]
		} else {
			think = reply + "</think>"
			reply = ""
		}
	}

	return reply, think
}
