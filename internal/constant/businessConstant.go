package constant

import "newsBot/internal/types"

const (
	Message_UserRole      = "user"
	Message_AssistantRole = "assistant"
	Message_SystemRole    = "system"

	SessionMessage_MessageType_User      = 1 //用户消息
	SessionMessage_MessageType_Assistant = 2 //回复消息
	SessionMessage_MessageType_System    = 3 //系统消息

	Session_SessionType_Normal           = 1 //普通会话
	Session_SessionType_IntelligentAgent = 2 //智能体会话

	IntelligentAgent_IntelligentAgentType_System = 1 // 系统默认
	IntelligentAgent_IntelligentAgentType_User   = 2 // 用户创建

	ApiPlatform_Firework    = "firework"
	ApiPlatform_OpenRouter  = "openRouter"
	ApiPlatform_Ali_baiLian = "aliBaiLian"

	// 阿里百炼模型常量
	ALiBaiLian_deepseek_r1 = "deepseek-r1"
)

var (
	apiConfigMap = map[string]types.ApiConfig{
		ApiPlatform_Firework: {
			Url:       "https://api.fireworks.ai/inference/v1/chat/completions",
			SecretKey: "fw_3ZJw3ENr76XfvUpyrppemNbN",
		},
		ApiPlatform_OpenRouter: {
			Url:       "https://openrouter.ai/api/v1/chat/completions",
			SecretKey: "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",
		},
		ApiPlatform_Ali_baiLian: {
			Url:       "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions",
			SecretKey: "sk-b462e4567723437a9a05667ecc19c45f", //测试 sdk
		},
	}
)

func ApiConfigMap() map[string]types.ApiConfig {
	return apiConfigMap
}

// GetModelMap 返回各平台支持的模型映射
func GetModelMap() map[string]map[string]string {
	return map[string]map[string]string{
		ApiPlatform_Firework: {
			"llama-3.1-405b": "llama-v3p1-405b-instruct",
			"llama-3.1-70b":  "llama-v3p1-70b-instruct",
			"llama-3.1-8b":   "llama-v3p1-8b-instruct",
		},
		ApiPlatform_OpenRouter: {
			"deepseek-r1":    "deepseek/deepseek-r1",
			"claude-3.5":     "anthropic/claude-3.5-sonnet",
			"gpt-4o":         "openai/gpt-4o",
			"llama-3.1-405b": "meta-llama/llama-3.1-405b-instruct",
		},
		ApiPlatform_Ali_baiLian: {
			ALiBaiLian_deepseek_r1: "deepseek-r1",
			"qwen-max":             "qwen-max",
			"qwen-plus":            "qwen-plus",
		},
	}
}
